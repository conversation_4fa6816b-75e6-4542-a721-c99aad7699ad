import streamlit as st
import psycopg2
import pandas as pd
from faker import Faker
import hashlib
import random
import uuid
import openai
import os

fake = Faker()
st.set_page_config(page_title="Data Masking Tool", layout="wide")

# --- 🧠 Masking Functions ---
def mask_name(x): return fake.name()
def mask_email(x): return fake.email()
def mask_phone(x): return fake.phone_number()
def mask_address(x): return fake.address()
def mask_date(x): return fake.date()
def mask_uuid(x): return str(uuid.uuid4())
def mask_hash(x): return hashlib.sha256(str(x).encode()).hexdigest()
def mask_random_int(x): return random.randint(1000, 9999)
def mask_none(x): return x

def mask_dob_shift(x):
    try:
        return pd.to_datetime(x) + pd.Timedelta(days=random.randint(-60, 60))
    except:
        return x

MASKING_FUNCTIONS = {
    "None": mask_none,
    "Name": mask_name,
    "Email": mask_email,
    "Phone": mask_phone,
    "Address": mask_address,
    "Date": mask_date,
    "UUID": mask_uuid,
    "Hash": mask_hash,
    "RandomInt": mask_random_int,
    "DOB Shift": mask_dob_shift
}

COLUMN_RULES = {
    "name": ["Name", "Hash", "None"],
    "email": ["Email", "Hash", "None"],
    "phone": ["Phone", "RandomInt", "None"],
    "address": ["Address", "None"],
    "date": ["Date", "DOB Shift", "None"],
    "uuid": ["UUID", "Hash"],
    "id": ["Hash", "UUID", "RandomInt"],
    "dob": ["DOB Shift", "Date", "None"]
}

# --- 🔌 Connect to DB ---
def connect_to_db(host, dbname, user, password, port):
    return psycopg2.connect(host=host, database=dbname, user=user, password=password, port=port)

st.title("🔐 Data Masking Tool for PostgreSQL")

# --- Sidebar DB connection ---
with st.sidebar:
    st.subheader("📦 Database Settings")
    host = st.text_input("Host", value="***********")
    dbname = st.text_input("Database Name", value="healthcare_dm")
    user = st.text_input("User", value="svc_postgresql")
    password = st.text_input("Password", type="password")
    port = st.text_input("Port", value="5454")

    if st.button("Connect"):
        try:
            conn = connect_to_db(host, dbname, user, password, port)
            st.session_state.conn = conn
            st.session_state.cursor = conn.cursor()
            st.success(f"Connected to {dbname}")
            st.session_state.connected = True
        except Exception as e:
            st.error(f"Connection failed: {e}")
            st.session_state.connected = False

if st.session_state.get("connected"):
    cursor = st.session_state.cursor
    if "tables" not in st.session_state:
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema='public';")
        st.session_state.tables = [row[0] for row in cursor.fetchall()]

    table_name = st.selectbox("📄 Choose a table to mask", st.session_state.tables)

    if st.button("🔄 Load Table"):
        df = pd.read_sql_query(f'SELECT * FROM "{table_name}"', st.session_state.conn)
        st.session_state.df = df.copy()
        st.session_state.table_loaded = True
        st.session_state.masking_rules_data = {col: "None" for col in df.columns}
        st.success("✅ Table loaded successfully")

# --- Masking Rules UI ---
if st.session_state.get("table_loaded"):
    st.subheader("🛡️ Select Masking Rules for Each Column")

    with st.form("masking_rules_form"):
        cols = st.session_state.df.columns.tolist()
        n_cols_per_row = 3

        for i in range(0, len(cols), n_cols_per_row):
            cols_row = st.columns(n_cols_per_row)
            for j, col in enumerate(cols[i:i+n_cols_per_row]):
                col_lower = col.lower()
                recommended = []
                for key, rules in COLUMN_RULES.items():
                    if key in col_lower:
                        recommended = rules
                        break
                options = recommended + [r for r in MASKING_FUNCTIONS.keys() if r not in recommended]
                current_value = st.session_state.masking_rules_data.get(col, "None")
                selected = cols_row[j].selectbox(f"{col}", options, index=options.index(current_value), key=f"mask_{col}")

        if st.form_submit_button("✅ Update Masking Rules"):
            for col in cols:
                val = st.session_state.get(f"mask_{col}", "None")
                st.session_state.masking_rules_data[col] = val
            st.success("Masking rules updated!")

    if st.button("🚀 Apply Masking"):
        masked_df = st.session_state.df.copy()
        for col, rule in st.session_state.masking_rules_data.items():
            func = MASKING_FUNCTIONS.get(rule, lambda x: x)
            masked_df[col] = masked_df[col].apply(func)

        st.session_state.masked_df = masked_df
        st.success("✅ Masking applied successfully!")

# --- Show masked output ---
if st.session_state.get("masked_df") is not None:
    st.subheader("📊 Preview Masked Data")
    st.dataframe(st.session_state.masked_df.head())
    csv = st.session_state.masked_df.to_csv(index=False).encode("utf-8")
    st.download_button("⬇️ Download Masked CSV", data=csv, file_name="masked_data.csv", mime="text/csv")