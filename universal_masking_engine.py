
import psycopg2
import pandas as pd
import random
import string
import datetime
import yaml

# -----------------------------------
# 🔐 Masking Functions
# -----------------------------------

def scramble(val):
    val = str(val)
    chars = list(val)
    random.shuffle(chars)
    return ''.join(chars)

def fake_email():
    domains = ["example.com", "testmail.com", "demo.org"]
    return f"user{random.randint(1000, 9999)}@{random.choice(domains)}"

def vary_dob(val):
    try:
        dt = datetime.datetime.strptime(str(val)[:10], "%Y-%m-%d")
        return dt.replace(year=dt.year + random.randint(-3, 3)).strftime("%Y-%m-%d")
    except:
        return val

def mask_ssn(val):
    return "XXX-XX-" + str(val)[-4:]

def mask_insurance_id(val):
    if not isinstance(val, str) or not val.startswith("PLAN"):
        return "PLANXXXXXXX"
    return "PLAN" + ''.join(random.choices(string.ascii_uppercase + string.digits, k=len(val[4:])))

def mask_address_id(val):
    if not isinstance(val, str) or not val.startswith("ADD"):
        return "ADDXXXXXXX"
    return "ADD" + ''.join(random.choices(string.ascii_uppercase + string.digits, k=len(val[3:])))

def nullify(_):
    return None

def fake_name():
    return random.choice(["Alice", "Bob", "Priya", "Ravi", "Nina", "John"])

# -----------------------------------
# 📌 Function Mapper
# -----------------------------------

MASKING_FUNCTIONS = {
    "scramble": scramble,
    "email_fake": fake_email,
    "mask_insurance_id": mask_insurance_id,
    "mask_address_id": mask_address_id,
    "mask_ssn": mask_ssn,
    "vary_dob": vary_dob,
    "nullify": nullify,
    "fake_name": fake_name
}

AUTO_MASKING_RULES = {
    "email": fake_email,
    "ssn": mask_ssn,
    "dob": vary_dob,
    "insurance_id": mask_insurance_id,
    "address_id": mask_address_id,
    "phone": lambda x: "07XXXXXXXXX"
}

# -----------------------------------
# 📂 Load Config
# -----------------------------------

with open("masking_config.yaml") as f:
    config = yaml.safe_load(f)

# -----------------------------------
# 🚀 Engine
# -----------------------------------

def connect(dbname):
    return psycopg2.connect(
        dbname="accounts_db",
        user="svc_postgresql",
        password="4YGwabLCTSHp85mEVBj0",  # CHANGE THIS
        host="***********",
        port="5454"
    )

def process_table(conn, dbname, table_cfg):
    source = table_cfg["name"]
    target = table_cfg.get("target", f"masked_{source}")
    masking = table_cfg.get("masking", {})

    print(f"🔄 Reading from: {dbname}.{source}")
    df = pd.read_sql_query(f"SELECT * FROM {source};", conn)

    masked_df = pd.DataFrame()

    for col in df.columns:
        if col in masking:
            func = MASKING_FUNCTIONS[masking[col]]
            print(f"🔐 Masking (config): {col} → {masking[col]}")
        elif col.lower() in AUTO_MASKING_RULES:
            func = AUTO_MASKING_RULES[col.lower()]
            print(f"🤖 Masking (auto): {col}")
        else:
            func = lambda x: x
            print(f"✅ No masking: {col}")

        masked_df[col] = df[col].apply(func)

    cursor = conn.cursor()

    # Create table if not exists
    cols_sql = ", ".join([f"{col} TEXT" for col in masked_df.columns])
    cursor.execute(f"CREATE TABLE IF NOT EXISTS {target} ({cols_sql});")

    # Insert data
    for _, row in masked_df.iterrows():
        placeholders = ", ".join(["%s"] * len(row))
        cursor.execute(f"INSERT INTO {target} VALUES ({placeholders});", tuple(row))

    conn.commit()
    print(f"✅ Inserted masked data into {target}\n")

# -----------------------------------
# 🔁 Loop Through Databases
# -----------------------------------

for db in config["databases"]:
    dbname = db["name"]
    try:
        conn = connect(dbname)
        print(f"🗂️ Connected to {dbname}")
        for tbl in db["tables"]:
            process_table(conn, dbname, tbl)
        conn.close()
    except Exception as e:
        print(f"❌ Failed on {dbname}: {e}")
