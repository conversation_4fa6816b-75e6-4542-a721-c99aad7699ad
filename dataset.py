from faker import Faker
import pandas as pd
import random

fake = Faker()
patients = []

for i in range(1_000_000):
    patients.append({
        "patient_id": i + 1,
        "name": fake.name(),
        "age": random.randint(0, 100),
        "gender": random.choice(["Male", "Female"]),
        "address": fake.address(),
        "phone": fake.phone_number(),
        "email": fake.email(),
        "date_of_birth": fake.date_of_birth(minimum_age=0, maximum_age=100)
    })

df_patients = pd.DataFrame(patients)
df_patients.to_csv("patients.csv", index=False)
