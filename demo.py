import psycopg2
import hashlib

# ✅ Hashing function
def hash_data(data: str) -> str:
    return hashlib.sha256(data.encode()).hexdigest()

# ✅ PostgreSQL connection
def connect_db():
    return psycopg2.connect(
        dbname="demo_data",
        user="svc_postgresql",           # Change if you use a different user
        password="4YGwabLCTSHp85mEVBj0",  # Replace with correct password
        host="***********",
        port="5454"
    )

# ✅ Create table if it doesn't exist
def create_patients_table():
    conn = connect_db()
    cursor = conn.cursor()

    create_query = """
    CREATE TABLE IF NOT EXISTS patients (
        id SERIAL PRIMARY KEY,
        name_hashed TEXT NOT NULL,
        age INTEGER,
        diagnosis TEXT
    );
    """
    cursor.execute(create_query)
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ Table 'patients' is ready.")

# ✅ Insert patient
def insert_patient(name, age, diagnosis):
    conn = connect_db()
    cursor = conn.cursor()

    hashed_name = hash_data(name)

    insert_query = """
    INSERT INTO patients (name_hashed, age, diagnosis)
    VALUES (%s, %s, %s)
    """
    cursor.execute(insert_query, (hashed_name, age, diagnosis))
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ Patient inserted.")

# ✅ Read patients
def read_patients():
    conn = connect_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM patients")
    rows = cursor.fetchall()
    print("📋 Patients:")
    for row in rows:
        print(row)
    cursor.close()
    conn.close()

# 🔁 Make sure this order is followed
if __name__ == "__main__":
    create_patients_table()  # ✅ Create the table first
    insert_patient("John Smith", 45, "Diabetes")
    insert_patient("Alice Moore", 34, "Hypertension")
    read_patients()
