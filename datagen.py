import psycopg2
import pandas as pd
import random
import datetime
import string


# Masking Functions


def scramble(value):
    value = str(value)
    chars = list(value)
    random.shuffle(chars)
    return ''.join(chars)

def fake_name():
    return random.choice(["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"])

def fake_email():
    domains = ["example.com", "testmail.com", "securemail.org"]
    return f"user{random.randint(1000,9999)}@{random.choice(domains)}"

def vary_dob(dob):
    try:
        date = datetime.datetime.strptime(str(dob)[:10], "%Y-%m-%d")
        offset = random.randint(-5, 5)
        new_date = date.replace(year=date.year + offset)
        return new_date.strftime("%Y-%m-%d")
    except:
        return dob

def mask_ssn(ssn):
    return "XXX-XX-" + str(ssn)[-4:]

def fake_phone():
    return random.choice(["07123456789", "07849039464", "07555555555"])

def fake_id():
    return random.randint(1000, 9999)


#  PostgreSQL Connection

def connect_db():
    return psycopg2.connect(
        dbname="healthcare_dm",
        user="svc_postgresql",
        password="4YGwabLCTSHp85mEVBj0",  
        host="***********",
        port="5454"
    )


#  Read Original Data


def load_original_data():
    conn = connect_db()
    query = "SELECT * FROM patient;"  #  Replace with your actual table name
    df = pd.read_sql_query(query, conn)
    conn.close()
    return df


#  Create Masked Table


def create_masked_table():
    conn = connect_db()
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS masked_patients (
            patient_id TEXT,
            first_name TEXT,
            last_name TEXT,
            dob TEXT,
            gender TEXT,
            email TEXT,
            phone_number TEXT,
            ssn TEXT,
            address_id INTEGER,
            created_at TIMESTAMP,
            insurance_id INTEGER
        );
    """)
    conn.commit()
    cursor.close()
    conn.close()
    print(" Table 'masked_patients' is ready.")


#  Insert Masked Data


def insert_masked_data(df):
    conn = connect_db()
    cursor = conn.cursor()

    for _, row in df.iterrows():
        cursor.execute("""
            INSERT INTO masked_patients (
                patient_id, first_name, last_name, dob, gender,
                email, phone_number, ssn, address_id, created_at, insurance_id
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            scramble(row["patient_id"]),
            fake_name(),
            fake_name(),
            vary_dob(row["dob"]),
            row["gender"],
            fake_email(),
            fake_phone(),
            mask_ssn(row["ssn"]),
            fake_id(),
            row["created_at"],
            fake_id()
        ))

    conn.commit()
    cursor.close()
    conn.close()
    print(" Masked data inserted into 'masked_patients'.")

def view_masked_data():
    conn = connect_db()
    df = pd.read_sql_query("SELECT * FROM masked_patients LIMIT 10;", conn)
    print("\n📋 Sample Masked Records:\n", df)
    conn.close()

# Add this after insert_masked_data(df)
view_masked_data()



# Main Execution


if __name__ == "__main__":
    print("Reading original data...")
    df = load_original_data()

    print(" Creating masked table...")
    create_masked_table()

    print(" Inserting masked data...")
    insert_masked_data(df)

    
