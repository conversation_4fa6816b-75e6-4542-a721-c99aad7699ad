import streamlit as st
import psycopg2
import pandas as pd
from faker import Faker
import hashlib
import random
import uuid

# Initialize Faker and page config
fake = Faker()
st.set_page_config(page_title="Data Masking Tool", layout="centered")

# --- 🧠 Masking Functions ---
def hash_value(val): return hashlib.sha256(str(val).encode()).hexdigest()
def fake_name(_): return fake.name()
def fake_email(_): return fake.email()
def fake_phone(_): return fake.phone_number()
def scramble(val): return ''.join(random.sample(str(val), len(str(val)))) if val else val
def fake_address(_): return fake.address().replace("\n", ", ")
def random_uuid(_): return str(uuid.uuid4())
def nullify(_): return None

MASKING_FUNCTIONS = {
    "None": lambda x: x,
    "hash": hash_value,
    "fake_name": fake_name,
    "fake_email": fake_email,
    "fake_phone": fake_phone,
    "fake_address": fake_address,
    "scramble": scramble,
    "uuid": random_uuid,
    "nullify": nullify,
}

# 🧠 Column-aware suggestions
COLUMN_RULES = {
    "name": ["fake_name", "scramble", "nullify"],
    "email": ["fake_email", "scramble", "nullify"],
    "phone": ["fake_phone", "scramble", "nullify"],
    "dob": ["scramble", "nullify"],
    "address": ["fake_address", "scramble", "nullify"],
    "id": ["hash", "uuid", "scramble", "nullify"]
}

# --- 🔌 Database Connection ---
def connect_to_db(host, dbname, user, password):
    return psycopg2.connect(host=host, database=dbname, user=user, password=password, port=port)

# ---  App UI ---
st.title(" Data Masking Tool for PostgreSQL")

# --- Sidebar DB Connection ---
with st.sidebar:
    st.subheader("Database Settings")
    host = st.text_input("Host", value="***********")
    dbname = st.text_input("Database Name", value="healthcare_dm")
    user = st.text_input("User", value="svc_postgresql")
    password = st.text_input("Password", type="password")
    port = st.text_input("Port", value="5454")

    if st.button("Connect"):
        try:
            conn = connect_to_db(host, dbname, user, password)
            st.session_state.conn = conn
            st.session_state.cursor = conn.cursor()
            st.success(f" Connected to {dbname}")
            st.session_state.connected = True
        except Exception as e:
            st.error(f" Connection failed: {e}")
            st.session_state.connected = False

# ---  Table Selection ---
if st.session_state.get("connected"):
    cursor = st.session_state.cursor

    if "tables" not in st.session_state:
        cursor.execute(
            "SELECT table_name FROM information_schema.tables WHERE table_schema='public';"
        )
        st.session_state.tables = [row[0] for row in cursor.fetchall()]

    table_name = st.selectbox("Choose a table to mask", st.session_state.tables)

    if st.button("Load Table"):
        df = pd.read_sql_query(f'SELECT * FROM "{table_name}"', st.session_state.conn)
        st.session_state.df = df.copy()
        st.session_state.table_loaded = True
        st.session_state.masking_rules = {col: "None" for col in df.columns}
        st.success(" Table loaded successfully")

# --- Masking Rules ---
if st.session_state.get("table_loaded"):
    st.subheader("Select Masking Rules")

    for col in st.session_state.df.columns:
        col_lower = col.lower()
        recommended = []

        for key, rules in COLUMN_RULES.items():
            if key in col_lower:
                recommended = rules
                break

        options = recommended + [r for r in MASKING_FUNCTIONS.keys() if r not in recommended]
        current_value = st.session_state.masking_rules.get(col, "None")
        selected = st.selectbox(f"{col}", options, index=options.index(current_value))
        st.session_state.masking_rules[col] = selected

    # ---  Apply Button ---
    if st.button("Apply Masking"):
        masked_df = st.session_state.df.copy()

        for col, rule in st.session_state.masking_rules.items():
            func = MASKING_FUNCTIONS.get(rule, lambda x: x)
            masked_df[col] = masked_df[col].apply(func)

        st.session_state.masked_df = masked_df
        st.success(" Masking applied successfully!")

# ---  Show masked output ---
if st.session_state.get("masked_df") is not None:
    st.subheader(" Preview Masked Data")
    st.dataframe(st.session_state.masked_df.head())

    csv = st.session_state.masked_df.to_csv(index=False).encode("utf-8")
    st.download_button(" Download Masked CSV", data=csv, file_name="masked_data.csv", mime="text/csv")
