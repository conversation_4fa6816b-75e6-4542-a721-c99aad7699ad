
import psycopg2
import pandas as pd
import random
import string
import datetime
import argparse


#  Masking Functions


def scramble(val):
    val = str(val)
    chars = list(val)
    random.shuffle(chars)
    return ''.join(chars)

# def fake_email():
#     domains = ["example.com", "testmail.com", "demo.org"]
#     return f"user{random.randint(1000, 9999)}@{random.choice(domains)}"
def fake_email(_):  # Accepts 1 unused argument
    domains = ["example.com", "testmail.com", "demo.org"]
    return f"user{random.randint(1000, 9999)}@{random.choice(domains)}"


def vary_dob(val):
    try:
        dt = datetime.datetime.strptime(str(val)[:10], "%Y-%m-%d")
        return dt.replace(year=dt.year + random.randint(-3, 3)).strftime("%Y-%m-%d")
    except:
        return val

def mask_ssn(val):
    return "XXX-XX-" + str(val)[-4:]

def mask_insurance_id(val):
    if not isinstance(val, str) or not val.startswith("PLAN"):
        return "PLANXXXXXXX"
    return "PLAN" + ''.join(random.choices(string.ascii_uppercase + string.digits, k=len(val[4:])))

def mask_address_id(val):
    if not isinstance(val, str) or not val.startswith("ADD"):
        return "ADDXXXXXXX"
    return "ADD" + ''.join(random.choices(string.ascii_uppercase + string.digits, k=len(val[3:])))

def nullify(_):
    return None

# def fake_name():
#     return random.choice(["Alice", "Bob", "Priya", "Ravi", "Nina", "John"])

def fake_name(_):
    return random.choice(["Alice", "Bob", "Priya", "Ravi", "Nina", "John"])



# Column Detection Rules


AUTO_MASKING_RULES = {
    "first_name": fake_name,
    "last_name": fake_name,
    "email": fake_email,
    "ssn": mask_ssn,
    "dob": vary_dob,
    "insurance_id": mask_insurance_id,
    "address_id": mask_address_id,
    "phone": lambda x: "07XXXXXXXXX",
    "name": fake_name,
    "patient_id": scramble,
    "client_number": scramble,
    
}

#  Connect to PostgreSQL


def connect(dbname):
    return psycopg2.connect(
        dbname=dbname,
        user="svc_postgresql",
        password="4YGwabLCTSHp85mEVBj0",  
        host="***********",
        port="5454"
    )


#  Main Logic


def mask_table(database, table_name):
    conn = connect(database)
    df = pd.read_sql_query(f'SELECT * FROM "{table_name}";', conn)
    print(f" Read {len(df)} rows from {database}.{table_name}")

    masked_df = pd.DataFrame()
    for col in df.columns:
        # match = next((func for key, func in AUTO_MASKING_RULES.items() if key in col.lower()), None)
        match = AUTO_MASKING_RULES.get(col.lower())
        if match:
            print(f" Masking: {col} → {match.__name__}")
            masked_df[col] = df[col].apply(match)
        else:
            print(f" Keeping: {col}")
            masked_df[col] = df[col]

    target_table = f"masked_{table_name}"
    cols_sql = ", ".join([f'"{col}" TEXT' for col in masked_df.columns])
    cursor = conn.cursor()
    cursor.execute(f'CREATE TABLE IF NOT EXISTS "{target_table}" ({cols_sql});')
    conn.commit()

    for _, row in masked_df.iterrows():
        placeholders = ", ".join(["%s"] * len(row))
        cursor.execute(f'INSERT INTO "{target_table}" VALUES ({placeholders});', tuple(row))

    conn.commit()
    print(f" Masked data inserted into {target_table}")
    cursor.close()
    conn.close()


#  Entry Point


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Mask sensitive data in PostgreSQL table.")
    parser.add_argument("--db", required=True, help="Database name")
    parser.add_argument("--table", required=True, help="Table name")
    args = parser.parse_args()

    mask_table(args.db, args.table)
