import streamlit as st
import pandas as pd
import psycopg2
from faker import Faker
import hashlib
import random

# Initialize Faker
faker = Faker()

# Utility functions
def hash_value(val):
    return hashlib.sha256(str(val).encode()).hexdigest()

def fake_name():
    return faker.name()

def fake_email():
    return faker.email()

def fake_address():
    return faker.address()

def fake_dob():
    return faker.dob

def scramble(val):
    val = str(val)
    val_list = list(val)
    random.shuffle(val_list)
    return ''.join(val_list)

def mask_column(series, rule):
    if rule == "None":
        return series
    elif rule == "hash":
        return series.apply(hash_value)
    elif rule == "fake_name":
        return series.apply(lambda _: fake_name())
    elif rule == "fake_email":
        return series.apply(lambda _: fake_email())
    elif rule == "fake_address":
        return series.apply(lambda _: fake_address())
    elif rule == "fake_dOB":
        return series.apply(lambda _: fake_dob())
    elif rule == "scramble":
        return series.apply(scramble)
    else:
        return series

# Session State Setup
if "table_data" not in st.session_state:
    st.session_state.table_data = None

if "masking_rules" not in st.session_state:
    st.session_state.masking_rules = {}

# UI
st.title(" Data Masking Tool for PostgreSQL")

# Connection Inputs
dbname = st.sidebar.text_input("Database Name", value="healthcare_dm")
user = st.sidebar.text_input("User", value="postgres")
password = st.sidebar.text_input("Password", type="password")
host = st.sidebar.text_input("Host", value="localhost")
port = st.sidebar.text_input("Port", value="5432")

# Connect and Fetch Tables
if st.sidebar.button("Connect"):
    try:
        conn = psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)
        cur = conn.cursor()
        cur.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
        tables = [row[0] for row in cur.fetchall()]
        st.session_state.conn = conn
        st.session_state.tables = tables
        st.success("Connected and tables loaded.")
    except Exception as e:
        st.error(f"Connection failed: {e}")

# If connected
if "tables" in st.session_state:
    selected_table = st.selectbox("Choose a table to mask", st.session_state.tables)
    if st.button("Load Table"):
        df = pd.read_sql_query(f'SELECT * FROM "{selected_table}";', st.session_state.conn)
        st.session_state.table_data = df
        st.session_state.masking_rules = {col: "None" for col in df.columns}

# Show table and masking options
if st.session_state.table_data is not None:
    st.subheader("Table Preview")
    st.dataframe(st.session_state.table_data.head())

    st.subheader("Select Masking Rules")
    for col in st.session_state.table_data.columns:
        rule = st.selectbox(
            f"{col}",
            ["None", "hash", "fake_name", "fake_email", "fake_address", "scramble"],
            index=["None", "hash", "fake_name", "fake_email", "fake_address", "scramble"].index(st.session_state.masking_rules.get(col, "None")),
            key=f"rule_{col}"
        )
        st.session_state.masking_rules[col] = rule

    if st.button("Apply Masking"):
        masked_df = st.session_state.table_data.copy()
        for col, rule in st.session_state.masking_rules.items():
            masked_df[col] = mask_column(masked_df[col], rule)

        st.subheader("Masked Data Preview")
        st.dataframe(masked_df.head())

        # Optional: Write back to DB (disabled for safety)
        # masked_df.to_sql(f"masked_{selected_table}", con=engine, index=False, if_exists='replace')
